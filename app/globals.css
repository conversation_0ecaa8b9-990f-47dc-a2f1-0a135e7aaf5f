@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    /* Sacred Design System - Light Theme */
    --background: 0 0% 99.6%; /* #FEFEFE - Pure off-white */
    --foreground: 0 0% 10.2%; /* #1A1A1A - Near black */
    --card: 0 0% 97.3%; /* #F8F9FA - Soft gray background */
    --card-foreground: 0 0% 10.2%;
    --popover: 0 0% 99.6%;
    --popover-foreground: 0 0% 10.2%;
    --primary: 210 45% 29%; /* #2D4A6B - Deep indigo (sacred) */
    --primary-foreground: 0 0% 98%;
    --secondary: 210 25% 93%; /* #E8EDF5 - Light indigo */
    --secondary-foreground: 210 45% 29%;
    --muted: 210 20% 96%;
    --muted-foreground: 215 16% 47%; /* #718096 - Light gray */
    --accent: 45 85% 52%; /* #D4AF37 - Warm gold */
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 20% 90%;
    --input: 210 20% 90%;
    --ring: 210 45% 29%;
    --sacred-teal: 195 37% 47%; /* #4A90A4 - Soft teal */
    --sacred-light: 210 25% 93%; /* #E8EDF5 - Light indigo for cards */
    --chart-1: 210 45% 29%;
    --chart-2: 195 37% 47%;
    --chart-3: 45 85% 52%;
    --chart-4: 210 25% 70%;
    --chart-5: 195 25% 70%;
    --radius: 0.75rem; /* Increased for softer feel */
  }
  .dark {
    /* Sacred Design System - Dark Theme */
    --background: 210 50% 6%; /* Deep sacred blue-black */
    --foreground: 0 0% 95%;
    --card: 210 40% 8%;
    --card-foreground: 0 0% 95%;
    --popover: 210 40% 8%;
    --popover-foreground: 0 0% 95%;
    --primary: 210 60% 65%; /* Lighter sacred indigo for dark mode */
    --primary-foreground: 210 50% 6%;
    --secondary: 210 30% 15%;
    --secondary-foreground: 0 0% 95%;
    --muted: 210 30% 12%;
    --muted-foreground: 215 20% 65%;
    --accent: 45 85% 60%; /* Brighter gold for dark mode */
    --accent-foreground: 210 50% 6%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 210 30% 18%;
    --input: 210 30% 18%;
    --ring: 210 60% 65%;
    --sacred-teal: 195 50% 60%;
    --sacred-light: 210 30% 15%;
    --chart-1: 210 60% 65%;
    --chart-2: 195 50% 60%;
    --chart-3: 45 85% 60%;
    --chart-4: 210 40% 50%;
    --chart-5: 195 40% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Sacred Typography */
.font-crimson {
  font-family: 'Crimson Text', serif;
  font-weight: 400;
  letter-spacing: -0.02em;
}

.font-crimson-bold {
  font-family: 'Crimson Text', serif;
  font-weight: 600;
  letter-spacing: -0.02em;
}

.font-playfair {
  font-family: var(--font-playfair);
}

.font-sans {
  font-family: var(--font-inter), ui-sans-serif, system-ui, sans-serif;
}

/* Sacred Design Elements */
.sacred-glow {
  box-shadow: 0 4px 20px rgba(45, 74, 107, 0.15), 0 1px 3px rgba(45, 74, 107, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sacred-glow:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(45, 74, 107, 0.25), 0 4px 6px rgba(45, 74, 107, 0.15);
}

.sacred-button {
  background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--sacred-teal)) 100%);
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sacred-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(45, 74, 107, 0.3);
}

.sacred-card {
  background: hsl(var(--sacred-light));
  border-radius: 16px;
  border: 1px solid hsl(var(--border));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sacred-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(45, 74, 107, 0.12);
}


.sacred-arch-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='80' height='80' viewBox='0 0 80 80' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M40 0C17.9 0 0 17.9 0 40h80C80 17.9 62.1 0 40 0z' fill='%232D4A6B' fill-opacity='0.015'/%3E%3Cpath d='M40 10C23.4 10 10 23.4 10 40h60C70 23.4 56.6 10 40 10z' fill='%234A90A4' fill-opacity='0.01'/%3E%3C/svg%3E");
  background-size: 160px 160px;
}

/* Mandala-inspired decorative patterns */
.mandala-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Ccircle cx='50' cy='50' r='45' fill='none' stroke='%232D4A6B' stroke-width='0.3' opacity='0.1'/%3E%3Ccircle cx='50' cy='50' r='35' fill='none' stroke='%234A90A4' stroke-width='0.2' opacity='0.08'/%3E%3Ccircle cx='50' cy='50' r='25' fill='none' stroke='%23D4AF37' stroke-width='0.15' opacity='0.06'/%3E%3C/svg%3E");
  background-size: 200px 200px;
  background-position: 0 0, 100px 100px;
}

/* Stained glass inspired gradient overlays */
.stained-glass-overlay {
  background: linear-gradient(
    45deg,
    rgba(45, 74, 107, 0.03) 0%,
    transparent 25%,
    rgba(212, 175, 55, 0.02) 50%,
    transparent 75%,
    rgba(74, 144, 164, 0.03) 100%
  );
}

/* Custom animations for chat messages */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

/* Chat background image optimizations */
.chat-background {
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
  transition: all 0.3s ease-in-out;
}

@media (max-width: 768px) {
  .chat-background {
    background-attachment: scroll;
    background-size: cover;
  }
}

/* Subtle animation for spiritual atmosphere */
@keyframes spiritual-glow {
  0%, 100% {
    opacity: 0.85;
  }
  50% {
    opacity: 0.95;
  }
}

.spiritual-overlay {
  animation: spiritual-glow 8s ease-in-out infinite;
}

/* Enhanced message bubble styling for better contrast */
.message-bubble-user {
  background: linear-gradient(135deg, rgb(168, 85, 247) 0%, rgb(236, 72, 153) 100%);
  box-shadow: 0 10px 25px -5px rgba(168, 85, 247, 0.25), 0 8px 10px -6px rgba(168, 85, 247, 0.1);
}

.message-bubble-assistant {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(229, 231, 235, 0.5);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05);
}

.dark .message-bubble-assistant {
  background: rgba(31, 41, 55, 0.95);
  border: 1px solid rgba(75, 85, 99, 0.5);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
}

@keyframes send-bounce {
  0%, 100% { transform: translateY(0);}
  50% { transform: translateY(-6px);}
}
.group-hover\:animate-send-bounce:hover .relative.z-10 {
  animation: send-bounce 0.4s;
}